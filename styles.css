/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
    overflow-x: hidden;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: white;
    font-size: 1.2rem;
    margin-top: 20px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-slide-up {
    animation: slideInUp 0.8s ease-out forwards;
}

.animate-zoom-in {
    animation: zoomIn 0.6s ease-out forwards;
}

/* Initial hidden state for animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered animations */
.animate-on-scroll[data-delay="0.1s"] {
    transition-delay: 0.1s;
}

.animate-on-scroll[data-delay="0.2s"] {
    transition-delay: 0.2s;
}

.animate-on-scroll[data-delay="0.3s"] {
    transition-delay: 0.3s;
}

.animate-on-scroll[data-delay="0.4s"] {
    transition-delay: 0.4s;
}

/* Hover animations for sections */
.parallax-section {
    transition: all 0.3s ease;
}

.parallax-section:hover {
    transform: translateY(-2px);
}

/* Interactive background elements */
.section-bg-element {
    position: absolute;
    pointer-events: none;
    z-index: 1;
}

/* Floating elements animation */
@keyframes floatUpDown {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.floating-element {
    animation: floatUpDown 4s ease-in-out infinite;
}

.floating-element:nth-child(2) {
    animation-delay: 1s;
}

.floating-element:nth-child(3) {
    animation-delay: 2s;
}

/* Section-specific animation states */
.section-visible {
    animation: sectionReveal 1s ease-out forwards;
}

@keyframes sectionReveal {
    from {
        opacity: 0.8;
    }
    to {
        opacity: 1;
    }
}

.about-animated::before {
    animation-duration: 12s;
}

.services-animated::before {
    animation-duration: 10s;
}

.services-animated::after {
    animation-duration: 15s;
}

.cars-animated::before {
    animation-duration: 15s;
}

.cars-animated::after {
    animation-duration: 20s;
}

.sell-animated::before {
    animation-duration: 18s;
}

.works-animated::before {
    animation-duration: 20s;
}

.contact-animated::before {
    animation-duration: 14s;
}

/* Enhanced hover effects for sections */
.parallax-section:hover::before {
    animation-play-state: paused;
}

.parallax-section:hover::after {
    animation-play-state: paused;
}

/* Smooth transitions for all animated elements */
* {
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Performance optimization for animations */
.car-card,
.service-card,
.step,
.section-title {
    will-change: transform, opacity;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transform: translateY(-100%);
    animation: slideDownHeader 0.8s ease-out 0.5s forwards;
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

@keyframes slideDownHeader {
    to {
        transform: translateY(0);
    }
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-image {
    height: 40px;
    width: auto;
    border-radius: 5px;
}

.logo-text {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
}

.nav-item {
    opacity: 0;
    animation: fadeInDown 0.6s ease-out forwards;
    animation-delay: calc(var(--i) * 0.15s + 1s);
}

.nav-item:nth-child(1) { --i: 1; }
.nav-item:nth-child(2) { --i: 2; }
.nav-item:nth-child(3) { --i: 3; }
.nav-item:nth-child(4) { --i: 4; }
.nav-item:nth-child(5) { --i: 5; }

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    position: relative;
    padding: 12px 20px;
    border-radius: 30px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    display: block;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: left 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: -1;
    border-radius: 30px;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
}

.nav-link:hover {
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.nav-link:hover::before {
    left: 0;
}

.nav-link:hover::after {
    width: 100%;
    height: 100%;
}

.nav-link:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    max-width: 600px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
    opacity: 0;
    animation: slideInUp 1s ease-out 1s forwards;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0;
    animation: slideInUp 1s ease-out 1.3s forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cta-button {
    display: inline-block;
    background: #fff;
    color: #667eea;
    padding: 18px 35px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: left 0.4s ease;
    z-index: -1;
    border-radius: 50px;
}

.cta-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: 1;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    color: white;
}

.cta-button:hover::before {
    left: 0;
}

.cta-button:hover::after {
    width: 100%;
    height: 100%;
}

.cta-button:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s ease;
}

/* Search Container */
.search-container {
    margin: 2rem 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0;
    animation: slideInUp 1s ease-out 1.6s forwards;
}

.search-box {
    display: flex;
    gap: 10px;
    background: rgba(255,255,255,0.95);
    padding: 15px;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.search-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.search-input {
    flex: 2;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
}

.search-select {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    background: white;
}

.search-button {
    padding: 15px 30px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-button:hover {
    background: #c0392b;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    opacity: 0;
    animation: slideInUp 1s ease-out 1.9s forwards;
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border: 3px solid white;
    position: relative;
    overflow: hidden;
}

.cta-button.secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: white;
    transition: left 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: -1;
}

.cta-button.secondary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: 1;
}

.cta-button.secondary:hover {
    color: #667eea;
    border-color: white;
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255,255,255,0.3);
}

.cta-button.secondary:hover::before {
    left: 0;
}

.cta-button.secondary:hover::after {
    width: 100%;
    height: 100%;
}

/* Floating Animation for CTA Buttons */
.cta-button {
    animation: float-button 3s ease-in-out infinite;
}

.cta-button:nth-child(2) {
    animation-delay: 1.5s;
}

@keyframes float-button {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Section Styles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
    position: relative;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.section-title.animate {
    opacity: 1;
    transform: translateY(0);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    transition: width 0.8s ease 0.3s;
}

.section-title.animate::after {
    width: 80px;
}

/* Parallax Effect */
.parallax-section {
    transform: translateY(0);
    transition: transform 0.1s ease-out;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(155, 89, 182, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(46, 204, 113, 0.05) 0%, transparent 50%);
    animation: aboutFloat 15s ease-in-out infinite;
    z-index: 1;
}

.about .container {
    position: relative;
    z-index: 2;
}

@keyframes aboutFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-20px) rotate(1deg);
        opacity: 0.9;
    }
    66% {
        transform: translateY(10px) rotate(-1deg);
        opacity: 0.8;
    }
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #555;
}

.placeholder-image {
    background: #ddd;
    height: 300px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #888;
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-item h3 {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%),
        radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
    animation: servicesWave 12s ease-in-out infinite;
    z-index: 1;
}

.services::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 20px,
            rgba(255,255,255,0.03) 20px,
            rgba(255,255,255,0.03) 40px
        );
    animation: servicesRotate 20s linear infinite;
    z-index: 1;
}

.services .container {
    position: relative;
    z-index: 2;
}

.services .section-title {
    color: white;
}

@keyframes servicesWave {
    0%, 100% {
        transform: translateX(0) scale(1);
    }
    50% {
        transform: translateX(20px) scale(1.05);
    }
}

@keyframes servicesRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(155, 89, 182, 0.05) 50%, transparent 70%);
    animation: contactPulse 16s ease-in-out infinite;
    z-index: 1;
}

.contact::after {
    content: '📧 📱 💬 🤝';
    position: absolute;
    bottom: 10%;
    left: -5%;
    font-size: 2rem;
    opacity: 0.04;
    animation: contactFloat 22s ease-in-out infinite;
    z-index: 1;
    white-space: nowrap;
}

.contact .container {
    position: relative;
    z-index: 2;
}

@keyframes contactPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: scale(1.05) rotate(1deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(0.95) rotate(0deg);
        opacity: 1;
    }
    75% {
        transform: scale(1.02) rotate(-1deg);
        opacity: 0.7;
    }
}

@keyframes contactFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
    }
    25% {
        transform: translateY(-10px) translateX(20px);
    }
    50% {
        transform: translateY(5px) translateX(40px);
    }
    75% {
        transform: translateY(-5px) translateX(60px);
    }
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.contact-info p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
}

.contact-icon {
    font-size: 1.2rem;
}

/* Contact Form */
.contact-form {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.submit-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
    width: 100%;
}

.submit-button:hover {
    transform: translateY(-2px);
}

/* Enhanced Contact Section */
.support-links {
    margin-top: 2rem;
}

.support-links h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.support-links ul {
    list-style: none;
    padding: 0;
}

.support-links li {
    margin-bottom: 0.5rem;
}

.support-links a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.support-links a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Cars Section */
.cars {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.cars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(231, 76, 60, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(46, 204, 113, 0.05) 50%, transparent 60%);
    animation: carsFloat 18s ease-in-out infinite;
    z-index: 1;
}

.cars::after {
    content: '🚗 🏎️ 🚙 🚕';
    position: absolute;
    top: 20%;
    left: -10%;
    font-size: 2rem;
    opacity: 0.03;
    animation: carsDrive 25s linear infinite;
    z-index: 1;
    white-space: nowrap;
}

.cars .container {
    position: relative;
    z-index: 2;
}

@keyframes carsFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-15px) scale(1.02);
        opacity: 0.9;
    }
    50% {
        transform: translateY(10px) scale(0.98);
        opacity: 1;
    }
    75% {
        transform: translateY(-5px) scale(1.01);
        opacity: 0.85;
    }
}

@keyframes carsDrive {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(120%);
    }
}

.cars-description {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Filter Bar */
.filter-bar {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-select {
    padding: 12px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 1rem;
    background: white;
    min-width: 150px;
    outline: none;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    border-color: #3498db;
}

.filter-button {
    padding: 12px 25px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.filter-button:hover {
    background: #2980b9;
}

.slider-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.slider-wrapper {
    overflow: hidden;
    width: 100%;
}

.slider-track {
    display: flex;
    transition: transform 0.5s ease-in-out;
    gap: 20px;
    padding: 20px;
}

.car-card {
    min-width: 300px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    animation: cardSlideIn 0.8s ease-out forwards;
    animation-delay: calc(var(--card-index) * 0.1s);
}

.car-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0,0,0,0.25);
}

.car-card:hover .car-image img {
    transform: scale(1.1) rotate(2deg);
}

@keyframes cardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Staggered animation for cards */
.car-card:nth-child(1) { --card-index: 1; }
.car-card:nth-child(2) { --card-index: 2; }
.car-card:nth-child(3) { --card-index: 3; }
.car-card:nth-child(4) { --card-index: 4; }
.car-card:nth-child(5) { --card-index: 5; }
.car-card:nth-child(6) { --card-index: 6; }
.car-card:nth-child(7) { --card-index: 7; }
.car-card:nth-child(8) { --card-index: 8; }
.car-card:nth-child(9) { --card-index: 9; }
.car-card:nth-child(10) { --card-index: 10; }
.car-card:nth-child(11) { --card-index: 11; }
.car-card:nth-child(12) { --card-index: 12; }
.car-card:nth-child(13) { --card-index: 13; }

/* Enhanced Car Badges */
.car-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 2;
}

.car-badge {
    background: #e74c3c;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transform: scale(0);
    animation: badgePop 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    animation-delay: calc(var(--badge-index) * 0.2s + 0.5s);
}

@keyframes badgePop {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(-90deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.car-badges .car-badge:nth-child(1) { --badge-index: 1; }
.car-badges .car-badge:nth-child(2) { --badge-index: 2; }

.car-badge.certified {
    background: #3498db;
}

.car-badge.featured {
    background: #f39c12;
}

.car-badge.low-miles {
    background: #27ae60;
}

.car-badge.financing {
    background: #9b59b6;
}

.car-badge.new-arrival {
    background: #e67e22;
}

.car-badge.warranty {
    background: #16a085;
}

.car-badge.best-value {
    background: #2ecc71;
}

.car-badge.inspected {
    background: #34495e;
}

/* Image Overlay */
.image-overlay {
    position: absolute;
    top: 0;
    right: 0;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.car-card:hover .image-overlay {
    opacity: 1;
}

.overlay-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    background: rgba(255,255,255,0.9);
    color: #333;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overlay-btn:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.favorite-btn:hover {
    color: #e74c3c;
}

.compare-btn:hover {
    color: #3498db;
}

.gallery-btn:hover {
    color: #f39c12;
}

.car-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.car-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.car-card:hover .car-image img {
    transform: scale(1.1);
}

.car-info {
    padding: 1.5rem;
}

/* Car Header */
.car-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.car-header h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    font-weight: bold;
    flex: 1;
    margin-right: 1rem;
}

.car-rating {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.9rem;
}

.stars {
    color: #f39c12;
    font-size: 1rem;
    margin-bottom: 2px;
}

.rating-text {
    color: #666;
    font-size: 0.8rem;
}

/* Price Section */
.price-section {
    margin-bottom: 1rem;
}

.car-price {
    font-size: 1.4rem;
    color: #e74c3c;
    font-weight: bold;
    margin-bottom: 0.2rem;
}

.price-estimate {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* Enhanced Car Details */
.car-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 1rem;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    color: #555;
}

.detail-icon {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.car-specs {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    padding: 8px 12px;
    background: #ecf0f1;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

/* Feature Tags */
.car-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 1rem;
}

.feature-tag {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.feature-tag:nth-child(2) {
    background: #e67e22;
}

.feature-tag:nth-child(3) {
    background: #9b59b6;
}

.car-actions {
    display: flex;
    gap: 10px;
    margin-top: 1rem;
}

/* Enhanced Buttons */
.btn-primary {
    flex: 1;
    padding: 14px 20px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
}

.btn-primary:hover::before {
    left: 0;
}

.btn-primary:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

.btn-primary span {
    font-size: 1rem;
    margin-bottom: 2px;
}

.btn-primary small {
    font-size: 0.75rem;
    opacity: 0.9;
    font-weight: normal;
}

.btn-secondary {
    flex: 1;
    padding: 14px 20px;
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Dealer Information */
.dealer-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.dealer-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.dealer-details {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.dealer-name {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.dealer-rating {
    font-size: 0.75rem;
    color: #666;
}

.dealer-rating .stars {
    color: #f39c12;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.slider-btn:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.prev-btn {
    left: 10px;
}

.next-btn {
    right: 10px;
}

.slider-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 2rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #667eea;
    transform: scale(1.2);
}

.dot:hover {
    background: #999;
}

/* Sell Car Section */
.sell-car {
    padding: 100px 0;
    background: white;
    position: relative;
    overflow: hidden;
}

.sell-car::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(ellipse at 30% 70%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 70% 30%, rgba(241, 196, 15, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, transparent 30%, rgba(52, 152, 219, 0.05) 50%, transparent 70%);
    animation: sellFloat 20s ease-in-out infinite;
    z-index: 1;
}

.sell-car::after {
    content: '💰 📈 ✨ 🎯';
    position: absolute;
    top: 10%;
    right: -10%;
    font-size: 1.5rem;
    opacity: 0.05;
    animation: sellIcons 30s linear infinite reverse;
    z-index: 1;
    white-space: nowrap;
}

.sell-car .container {
    position: relative;
    z-index: 2;
}

@keyframes sellFloat {
    0%, 100% {
        transform: translateX(0px) rotate(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateX(15px) rotate(0.5deg);
        opacity: 0.9;
    }
    66% {
        transform: translateX(-10px) rotate(-0.5deg);
        opacity: 0.8;
    }
}

@keyframes sellIcons {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-120%);
    }
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.sell-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.sell-info h3 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 2rem;
}

.sell-benefits {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.benefit-icon {
    background: #27ae60;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.benefit-text h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.benefit-text p {
    color: #666;
    line-height: 1.5;
}

.sell-form-container {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.sell-form h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 500;
}

.sell-form input,
.sell-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.sell-form input:focus,
.sell-form select:focus {
    outline: none;
    border-color: #3498db;
}

/* How It Works Section */
.how-it-works {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.how-it-works::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        conic-gradient(from 0deg at 20% 20%, rgba(52, 152, 219, 0.1) 0deg, transparent 60deg),
        conic-gradient(from 120deg at 80% 80%, rgba(155, 89, 182, 0.1) 0deg, transparent 60deg),
        radial-gradient(circle at 50% 50%, rgba(46, 204, 113, 0.05) 0%, transparent 50%);
    animation: worksRotate 25s linear infinite;
    z-index: 1;
}

.how-it-works::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 10%;
    width: 80%;
    height: 60%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 50px,
            rgba(52, 152, 219, 0.02) 50px,
            rgba(52, 152, 219, 0.02) 100px
        );
    animation: worksSlide 15s ease-in-out infinite;
    z-index: 1;
}

.how-it-works .container {
    position: relative;
    z-index: 2;
}

@keyframes worksRotate {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes worksSlide {
    0%, 100% {
        transform: translateX(0px);
        opacity: 0.5;
    }
    50% {
        transform: translateX(20px);
        opacity: 0.8;
    }
}

.process-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-button {
    padding: 15px 30px;
    background: white;
    color: #666;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.tab-button:hover {
    border-color: #3498db;
    color: #3498db;
}

.tab-button.active:hover {
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.step {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateY(-5px);
}

.step-number {
    width: 60px;
    height: 60px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
}

.step-content h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.step-content p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 40%, rgba(52, 152, 219, 0.1) 50%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(155, 89, 182, 0.1) 0%, transparent 50%);
    animation: footerWave 20s ease-in-out infinite;
    z-index: 1;
}

.footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        #3498db 25%,
        #e74c3c 50%,
        #f39c12 75%,
        transparent 100%);
    animation: footerLine 8s ease-in-out infinite;
    z-index: 2;
}

.footer .container {
    position: relative;
    z-index: 2;
}

@keyframes footerWave {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-10px) scale(1.02);
        opacity: 0.9;
    }
    66% {
        transform: translateY(5px) scale(0.98);
        opacity: 0.8;
    }
}

@keyframes footerLine {
    0%, 100% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(100%);
    }
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #3498db;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section p {
    line-height: 1.6;
    color: #bdc3c7;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 1rem;
    text-align: center;
    color: #95a5a6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .nav-menu {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    /* Cars section responsive */
    .car-card {
        min-width: 280px;
    }

    .slider-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .prev-btn {
        left: 5px;
    }

    .next-btn {
        right: 5px;
    }

    .cars-description {
        font-size: 1rem;
        padding: 0 1rem;
    }

    /* Responsive for new sections */
    .search-box {
        flex-direction: column;
        gap: 15px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-bar {
        flex-direction: column;
        align-items: center;
    }

    .sell-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .process-tabs {
        flex-direction: column;
        align-items: center;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .car-actions {
        flex-direction: column;
    }

    /* Enhanced card responsive */
    .car-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .car-header h3 {
        margin-right: 0;
    }

    .car-rating {
        align-items: flex-start;
    }

    .car-details {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .car-features {
        justify-content: center;
    }

    .car-badges {
        flex-direction: row;
        flex-wrap: wrap;
        max-width: 200px;
    }

    .image-overlay {
        opacity: 1;
        flex-direction: row;
        top: auto;
        bottom: 15px;
        right: 15px;
        left: 15px;
        justify-content: center;
    }

    .dealer-info {
        justify-content: center;
        text-align: center;
    }
}
