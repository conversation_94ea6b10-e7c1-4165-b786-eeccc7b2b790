<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoDeals - Buy & Sell Cars Online</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <h1 class="logo">🚗 AutoDeals</h1>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="#cars" class="nav-link">Buy Cars</a>
                    </li>
                    <li class="nav-item">
                        <a href="#sell" class="nav-link">Sell Car</a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">Contact</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Buy & Sell Cars Online</h1>
            <p class="hero-subtitle">Find your perfect car or sell your current one with ease</p>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" placeholder="Search by make, model, or keyword..." class="search-input">
                    <select class="search-select">
                        <option value="">All Brands</option>
                        <option value="bmw">BMW</option>
                        <option value="mercedes">Mercedes</option>
                        <option value="audi">Audi</option>
                        <option value="porsche">Porsche</option>
                        <option value="ferrari">Ferrari</option>
                        <option value="lamborghini">Lamborghini</option>
                    </select>
                    <button class="search-button">Search Cars</button>
                </div>
            </div>

            <div class="hero-buttons">
                <a href="#cars" class="cta-button primary">Browse Cars</a>
                <a href="#sell" class="cta-button secondary">Sell Your Car</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About AutoDeals</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>AutoDeals is your trusted online car marketplace, connecting buyers and sellers across the country. With over 10,000 verified listings and a commitment to transparency, we make car buying and selling simple, safe, and reliable.</p>
                    <p>Our platform offers comprehensive vehicle history reports, professional inspections, and secure payment processing to ensure every transaction is smooth and trustworthy.</p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <h3>10,000+</h3>
                            <p>Cars Listed</p>
                        </div>
                        <div class="stat-item">
                            <h3>50,000+</h3>
                            <p>Happy Customers</p>
                        </div>
                        <div class="stat-item">
                            <h3>500+</h3>
                            <p>Dealers</p>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="placeholder-image">
                        <span>🚗 Trusted Car Marketplace</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <h2 class="section-title">Our Services</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🛒</div>
                    <h3>Buy Cars</h3>
                    <p>Browse thousands of verified vehicles with detailed history reports and professional inspections.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">�</div>
                    <h3>Sell Your Car</h3>
                    <p>List your vehicle for free and reach thousands of potential buyers with our marketing tools.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">�</div>
                    <h3>Vehicle Inspection</h3>
                    <p>Professional 150-point inspections to ensure quality and transparency for all listings.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">💳</div>
                    <h3>Financing</h3>
                    <p>Get pre-approved for auto loans with competitive rates from our trusted lending partners.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Cars Section -->
    <section id="cars" class="cars">
        <div class="container">
            <h2 class="section-title">Featured Cars for Sale</h2>
            <p class="cars-description">Browse our verified inventory of quality pre-owned vehicles</p>

            <!-- Filter Bar -->
            <div class="filter-bar">
                <select class="filter-select">
                    <option value="">All Brands</option>
                    <option value="lamborghini">Lamborghini</option>
                    <option value="ferrari">Ferrari</option>
                    <option value="porsche">Porsche</option>
                    <option value="bmw">BMW</option>
                    <option value="audi">Audi</option>
                </select>
                <select class="filter-select">
                    <option value="">Price Range</option>
                    <option value="0-100000">Under $100k</option>
                    <option value="100000-200000">$100k - $200k</option>
                    <option value="200000-500000">$200k - $500k</option>
                    <option value="500000+">$500k+</option>
                </select>
                <select class="filter-select">
                    <option value="">Year</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                </select>
                <button class="filter-button">Apply Filters</button>
            </div>

            <div class="slider-container">
                <button class="slider-btn prev-btn" onclick="moveSlider(-1)">❮</button>
                <div class="slider-wrapper">
                    <div class="slider-track" id="sliderTrack">
                        <!-- Car Card 1 -->
                        <div class="car-card">
                            <div class="car-badge">Certified</div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=400&h=250&fit=crop" alt="Lamborghini Huracan" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>2022 Lamborghini Huracan</h3>
                                <p class="car-price">$250,000</p>
                                <p class="car-details">15,000 miles • Los Angeles, CA</p>
                                <p class="car-specs">V10 Engine • 630 HP • 0-60 in 2.9s</p>
                                <div class="car-actions">
                                    <button class="btn-primary">Buy Now</button>
                                    <button class="btn-secondary">View Details</button>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 2 -->
                        <div class="car-card">
                            <div class="car-badge">Featured</div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=400&h=250&fit=crop" alt="Ferrari 488" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>2023 Ferrari 488 GTB</h3>
                                <p class="car-price">$280,000</p>
                                <p class="car-details">8,500 miles • Miami, FL</p>
                                <p class="car-specs">V8 Twin-Turbo • 661 HP • 0-60 in 3.0s</p>
                                <div class="car-actions">
                                    <button class="btn-primary">Buy Now</button>
                                    <button class="btn-secondary">View Details</button>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 3 -->
                        <div class="car-card">
                            <div class="car-badge">Low Miles</div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=250&fit=crop" alt="Porsche 911" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>2022 Porsche 911 Turbo S</h3>
                                <p class="car-price">$230,000</p>
                                <p class="car-details">12,000 miles • New York, NY</p>
                                <p class="car-specs">H6 Twin-Turbo • 640 HP • 0-60 in 2.6s</p>
                                <div class="car-actions">
                                    <button class="btn-primary">Buy Now</button>
                                    <button class="btn-secondary">View Details</button>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 4 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=250&fit=crop" alt="BMW M8" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>BMW M8 Competition</h3>
                                <p class="car-price">$150,000</p>
                                <p class="car-specs">V8 Twin-Turbo • 617 HP • 0-60 in 3.2s</p>
                            </div>
                        </div>

                        <!-- Car Card 5 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=250&fit=crop" alt="Audi R8" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Audi R8 V10 Plus</h3>
                                <p class="car-price">$200,000</p>
                                <p class="car-specs">V10 • 602 HP • 0-60 in 3.2s</p>
                            </div>
                        </div>

                        <!-- Car Card 6 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=250&fit=crop" alt="McLaren 720S" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>McLaren 720S</h3>
                                <p class="car-price">$300,000</p>
                                <p class="car-specs">V8 Twin-Turbo • 710 HP • 0-60 in 2.8s</p>
                            </div>
                        </div>

                        <!-- Car Card 7 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=400&h=250&fit=crop" alt="Mercedes AMG GT" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Mercedes AMG GT R</h3>
                                <p class="car-price">$180,000</p>
                                <p class="car-specs">V8 Twin-Turbo • 577 HP • 0-60 in 3.5s</p>
                            </div>
                        </div>

                        <!-- Car Card 8 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=250&fit=crop" alt="Nissan GT-R" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Nissan GT-R Nismo</h3>
                                <p class="car-price">$220,000</p>
                                <p class="car-specs">V6 Twin-Turbo • 600 HP • 0-60 in 2.9s</p>
                            </div>
                        </div>

                        <!-- Car Card 9 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1494905998402-395d579af36f?w=400&h=250&fit=crop" alt="Chevrolet Corvette" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Chevrolet Corvette Z06</h3>
                                <p class="car-price">$110,000</p>
                                <p class="car-specs">V8 Supercharged • 670 HP • 0-60 in 2.6s</p>
                            </div>
                        </div>

                        <!-- Car Card 10 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=400&h=250&fit=crop" alt="Aston Martin" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Aston Martin DB11</h3>
                                <p class="car-price">$220,000</p>
                                <p class="car-specs">V12 Twin-Turbo • 630 HP • 0-60 in 3.7s</p>
                            </div>
                        </div>



                        <!-- Car Card 13 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1592198084033-aade902d1aae?w=400&h=250&fit=crop" alt="Lotus Evija" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Lotus Evija</h3>
                                <p class="car-price">$2,100,000</p>
                                <p class="car-specs">Electric • 1972 HP • 0-60 in 2.3s</p>
                            </div>
                        </div>

                        <!-- Car Card 14 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=400&h=250&fit=crop" alt="Koenigsegg Regera" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Koenigsegg Regera</h3>
                                <p class="car-price">$1,900,000</p>
                                <p class="car-specs">V8 Hybrid • 1500 HP • 0-60 in 2.8s</p>
                            </div>
                        </div>

                        <!-- Car Card 15 -->
                        <div class="car-card">
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1617814076367-b759c7d7e738?w=400&h=250&fit=crop" alt="Pagani Huayra" loading="lazy">
                            </div>
                            <div class="car-info">
                                <h3>Pagani Huayra</h3>
                                <p class="car-price">$2,800,000</p>
                                <p class="car-specs">V12 Twin-Turbo • 730 HP • 0-60 in 3.2s</p>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="slider-btn next-btn" onclick="moveSlider(1)">❯</button>
            </div>

            <div class="slider-dots" id="sliderDots">
                <!-- Dots will be generated by JavaScript -->
            </div>
        </div>
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Let's Connect</h3>
                    <p>Ready to start your next project? I'd love to hear from you!</p>
                    <div class="contact-details">
                        <div class="contact-item">
                            <span class="contact-icon">📧</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📱</span>
                            <span>+1 (555) 123-4567</span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📍</span>
                            <span>Your City, Country</span>
                        </div>
                    </div>
                </div>
                <form class="contact-form">
                    <div class="form-group">
                        <input type="text" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-button">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 MyWebsite. All rights reserved.</p>
        </div>
    </footer>

    <script>
        let currentSlide = 0;
        const totalSlides = 13;
        const slidesToShow = 3; // Number of cards to show at once
        const maxSlide = totalSlides - slidesToShow;

        function moveSlider(direction) {
            currentSlide += direction;

            if (currentSlide < 0) {
                currentSlide = 0;
            } else if (currentSlide > maxSlide) {
                currentSlide = maxSlide;
            }

            updateSlider();
        }

        function goToSlide(slideIndex) {
            currentSlide = slideIndex;
            if (currentSlide > maxSlide) {
                currentSlide = maxSlide;
            }
            updateSlider();
        }

        function updateSlider() {
            const sliderTrack = document.getElementById('sliderTrack');
            const cardWidth = 320; // Width of each card including margin
            const translateX = -currentSlide * cardWidth;

            sliderTrack.style.transform = `translateX(${translateX}px)`;

            // Update dots
            updateDots();
        }

        function updateDots() {
            const dots = document.querySelectorAll('.dot');
            dots.forEach((dot, index) => {
                dot.classList.remove('active');
                if (index === currentSlide) {
                    dot.classList.add('active');
                }
            });
        }

        function createDots() {
            const dotsContainer = document.getElementById('sliderDots');
            for (let i = 0; i <= maxSlide; i++) {
                const dot = document.createElement('span');
                dot.className = 'dot';
                if (i === 0) dot.classList.add('active');
                dot.onclick = () => goToSlide(i);
                dotsContainer.appendChild(dot);
            }
        }

        // Auto-slide functionality
        function autoSlide() {
            currentSlide++;
            if (currentSlide > maxSlide) {
                currentSlide = 0;
            }
            updateSlider();
        }

        // Initialize slider
        document.addEventListener('DOMContentLoaded', function() {
            createDots();

            // Auto-slide every 4 seconds
            setInterval(autoSlide, 4000);

            // Pause auto-slide on hover
            const sliderContainer = document.querySelector('.slider-container');
            let autoSlideInterval;

            sliderContainer.addEventListener('mouseenter', () => {
                clearInterval(autoSlideInterval);
            });

            sliderContainer.addEventListener('mouseleave', () => {
                autoSlideInterval = setInterval(autoSlide, 4000);
            });
        });
    </script>
</body>
</html>
