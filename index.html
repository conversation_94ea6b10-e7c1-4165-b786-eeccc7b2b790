<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoDeals - Buy & Sell Cars Online</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <img src="logo.jpg" alt="AutoDeals Logo" class="logo-image">
                    <span class="logo-text">AutoDeals</span>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="#cars" class="nav-link">Buy Cars</a>
                    </li>
                    <li class="nav-item">
                        <a href="#showroom" class="nav-link">Showroom</a>
                    </li>
                    <li class="nav-item">
                        <a href="#sell" class="nav-link">Sell Car</a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">Contact</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Buy & Sell Cars Online</h1>
            <p class="hero-subtitle">Find your perfect car or sell your current one with ease</p>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" placeholder="Search by make, model, or keyword..." class="search-input">
                    <select class="search-select">
                        <option value="">All Brands</option>
                        <option value="bmw">BMW</option>
                        <option value="mercedes">Mercedes</option>
                        <option value="audi">Audi</option>
                        <option value="porsche">Porsche</option>
                        <option value="ferrari">Ferrari</option>
                        <option value="lamborghini">Lamborghini</option>
                    </select>
                    <button class="search-button">Search Cars</button>
                </div>
            </div>

            <div class="hero-buttons">
                <a href="#cars" class="cta-button primary">Browse Cars</a>
                <a href="#sell" class="cta-button secondary">Sell Your Car</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about parallax-section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">About AutoDeals</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>AutoDeals is your trusted online car marketplace, connecting buyers and sellers across the country. With over 10,000 verified listings and a commitment to transparency, we make car buying and selling simple, safe, and reliable.</p>
                    <p>Our platform offers comprehensive vehicle history reports, professional inspections, and secure payment processing to ensure every transaction is smooth and trustworthy.</p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <h3>10,000+</h3>
                            <p>Cars Listed</p>
                        </div>
                        <div class="stat-item">
                            <h3>50,000+</h3>
                            <p>Happy Customers</p>
                        </div>
                        <div class="stat-item">
                            <h3>500+</h3>
                            <p>Dealers</p>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="placeholder-image">
                        <span>🚗 Trusted Car Marketplace</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services parallax-section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Our Services</h2>
            <div class="services-grid">
                <div class="service-card animate-on-scroll">
                    <div class="service-icon">🛒</div>
                    <h3>Buy Cars</h3>
                    <p>Browse thousands of verified vehicles with detailed history reports and professional inspections.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">�</div>
                    <h3>Sell Your Car</h3>
                    <p>List your vehicle for free and reach thousands of potential buyers with our marketing tools.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">�</div>
                    <h3>Vehicle Inspection</h3>
                    <p>Professional 150-point inspections to ensure quality and transparency for all listings.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">💳</div>
                    <h3>Financing</h3>
                    <p>Get pre-approved for auto loans with competitive rates from our trusted lending partners.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Cars Section -->
    <section id="cars" class="cars parallax-section">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Featured Cars for Sale</h2>
            <p class="cars-description">Browse our verified inventory of quality pre-owned vehicles</p>

            <!-- Filter Bar -->
            <div class="filter-bar">
                <select class="filter-select">
                    <option value="">All Brands</option>
                    <option value="lamborghini">Lamborghini</option>
                    <option value="ferrari">Ferrari</option>
                    <option value="porsche">Porsche</option>
                    <option value="bmw">BMW</option>
                    <option value="audi">Audi</option>
                </select>
                <select class="filter-select">
                    <option value="">Price Range</option>
                    <option value="0-100000">Under $100k</option>
                    <option value="100000-200000">$100k - $200k</option>
                    <option value="200000-500000">$200k - $500k</option>
                    <option value="500000+">$500k+</option>
                </select>
                <select class="filter-select">
                    <option value="">Year</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                </select>
                <button class="filter-button">Apply Filters</button>
            </div>

            <div class="slider-container">
                <button class="slider-btn prev-btn" onclick="moveSlider(-1)">❮</button>
                <div class="slider-wrapper">
                    <div class="slider-track" id="sliderTrack">
                        <!-- Car Card 1 -->
                        <div class="car-card" data-brand="lamborghini" data-price="250000" data-year="2022">
                            <div class="car-badges">
                                <div class="car-badge certified">Certified</div>
                                <div class="car-badge financing">Financing Available</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=400&h=250&fit=crop" alt="Lamborghini Huracan" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2022 Lamborghini Huracan</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.8)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$250,000</p>
                                    <p class="price-estimate">Est. $4,200/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>15,000 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Los Angeles, CA</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Manual</span>
                                    </div>
                                </div>
                                <p class="car-specs">V10 Engine • 630 HP • 0-60 in 2.9s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Leather Interior</span>
                                    <span class="feature-tag">Carbon Fiber</span>
                                    <span class="feature-tag">Sport Package</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2022 Lamborghini Huracan')">
                                        <span>Buy Now</span>
                                        <small>or $4,200/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2022 Lamborghini Huracan')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Premium Auto Gallery</span>
                                        <span class="dealer-rating">★★★★★ (4.9)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 2 -->
                        <div class="car-card" data-brand="ferrari" data-price="280000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge featured">Featured</div>
                                <div class="car-badge new-arrival">New Arrival</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=400&h=250&fit=crop" alt="Ferrari 488" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 Ferrari 488 GTB</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.9)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$280,000</p>
                                    <p class="price-estimate">Est. $4,700/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>8,500 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Miami, FL</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Twin-Turbo • 661 HP • 0-60 in 3.0s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Racing Seats</span>
                                    <span class="feature-tag">Track Package</span>
                                    <span class="feature-tag">Carbon Brakes</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 Ferrari 488 GTB')">
                                        <span>Buy Now</span>
                                        <small>or $4,700/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 Ferrari 488 GTB')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Exotic Motors Miami</span>
                                        <span class="dealer-rating">★★★★★ (4.8)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 3 -->
                        <div class="car-card" data-brand="porsche" data-price="230000" data-year="2022">
                            <div class="car-badges">
                                <div class="car-badge low-miles">Low Miles</div>
                                <div class="car-badge warranty">Warranty</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=400&h=250&fit=crop" alt="Porsche 911" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2022 Porsche 911 Turbo S</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.7)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$230,000</p>
                                    <p class="price-estimate">Est. $3,900/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>12,000 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>New York, NY</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>PDK Auto</span>
                                    </div>
                                </div>
                                <p class="car-specs">H6 Twin-Turbo • 640 HP • 0-60 in 2.6s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Sport Chrono</span>
                                    <span class="feature-tag">PCCB Brakes</span>
                                    <span class="feature-tag">Alcantara</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2022 Porsche 911 Turbo S')">
                                        <span>Buy Now</span>
                                        <small>or $3,900/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2022 Porsche 911 Turbo S')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Manhattan Porsche</span>
                                        <span class="dealer-rating">★★★★★ (4.9)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 4 -->
                        <div class="car-card" data-brand="bmw" data-price="150000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge best-value">Best Value</div>
                                <div class="car-badge inspected">Inspected</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=250&fit=crop" alt="BMW M8" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 BMW M8 Competition</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★☆</span>
                                        <span class="rating-text">(4.6)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$150,000</p>
                                    <p class="price-estimate">Est. $2,500/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>22,000 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Chicago, IL</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Twin-Turbo • 617 HP • 0-60 in 3.2s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">M Performance</span>
                                    <span class="feature-tag">Carbon Roof</span>
                                    <span class="feature-tag">Harman Kardon</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 BMW M8 Competition')">
                                        <span>Buy Now</span>
                                        <small>or $2,500/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 BMW M8 Competition')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">BMW of Chicago</span>
                                        <span class="dealer-rating">★★★★☆ (4.5)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 5 -->
                        <div class="car-card" data-brand="audi" data-price="200000" data-year="2022">
                            <div class="car-badges">
                                <div class="car-badge warranty">Extended Warranty</div>
                                <div class="car-badge inspected">Inspected</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=250&fit=crop" alt="Audi R8" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2022 Audi R8 V10 Plus</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★☆</span>
                                        <span class="rating-text">(4.5)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$200,000</p>
                                    <p class="price-estimate">Est. $3,400/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>18,500 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Dallas, TX</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V10 • 602 HP • 0-60 in 3.2s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Quattro AWD</span>
                                    <span class="feature-tag">Bang & Olufsen</span>
                                    <span class="feature-tag">Carbon Trim</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2022 Audi R8 V10 Plus')">
                                        <span>Buy Now</span>
                                        <small>or $3,400/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2022 Audi R8 V10 Plus')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Audi Dallas</span>
                                        <span class="dealer-rating">★★★★☆ (4.4)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 6 -->
                        <div class="car-card" data-brand="mclaren" data-price="300000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge featured">Featured</div>
                                <div class="car-badge low-miles">Low Miles</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=250&fit=crop" alt="McLaren 720S" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 McLaren 720S</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.9)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$300,000</p>
                                    <p class="price-estimate">Est. $5,100/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>6,200 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Beverly Hills, CA</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Twin-Turbo • 710 HP • 0-60 in 2.8s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Track Pack</span>
                                    <span class="feature-tag">Carbon Fiber</span>
                                    <span class="feature-tag">MSO Options</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 McLaren 720S')">
                                        <span>Buy Now</span>
                                        <small>or $5,100/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 McLaren 720S')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">McLaren Beverly Hills</span>
                                        <span class="dealer-rating">★★★★★ (4.8)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 7 -->
                        <div class="car-card" data-brand="mercedes" data-price="180000" data-year="2022">
                            <div class="car-badges">
                                <div class="car-badge certified">Certified</div>
                                <div class="car-badge financing">Financing Available</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=400&h=250&fit=crop" alt="Mercedes AMG GT" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2022 Mercedes AMG GT R</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★☆</span>
                                        <span class="rating-text">(4.6)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$180,000</p>
                                    <p class="price-estimate">Est. $3,050/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>14,800 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Atlanta, GA</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Twin-Turbo • 577 HP • 0-60 in 3.5s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">AMG Performance</span>
                                    <span class="feature-tag">Track Package</span>
                                    <span class="feature-tag">Burmester Audio</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2022 Mercedes AMG GT R')">
                                        <span>Buy Now</span>
                                        <small>or $3,050/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2022 Mercedes AMG GT R')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Mercedes-Benz Atlanta</span>
                                        <span class="dealer-rating">★★★★☆ (4.3)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 8 -->
                        <div class="car-card" data-brand="nissan" data-price="220000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge new-arrival">New Arrival</div>
                                <div class="car-badge best-value">Best Value</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=250&fit=crop" alt="Nissan GT-R" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 Nissan GT-R Nismo</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★☆</span>
                                        <span class="rating-text">(4.7)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$220,000</p>
                                    <p class="price-estimate">Est. $3,700/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>9,500 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Phoenix, AZ</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V6 Twin-Turbo • 600 HP • 0-60 in 2.9s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Nismo Package</span>
                                    <span class="feature-tag">Carbon Fiber</span>
                                    <span class="feature-tag">Bose Audio</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 Nissan GT-R Nismo')">
                                        <span>Buy Now</span>
                                        <small>or $3,700/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 Nissan GT-R Nismo')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Nissan Performance Center</span>
                                        <span class="dealer-rating">★★★★☆ (4.6)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 9 -->
                        <div class="car-card" data-brand="chevrolet" data-price="110000" data-year="2024">
                            <div class="car-badges">
                                <div class="car-badge best-value">Best Value</div>
                                <div class="car-badge warranty">Warranty</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1494905998402-395d579af36f?w=400&h=250&fit=crop" alt="Chevrolet Corvette" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2024 Chevrolet Corvette Z06</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.8)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$110,000</p>
                                    <p class="price-estimate">Est. $1,850/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>2,100 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Detroit, MI</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Manual</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Supercharged • 670 HP • 0-60 in 2.6s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Z06 Package</span>
                                    <span class="feature-tag">Magnetic Ride</span>
                                    <span class="feature-tag">Performance Tires</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2024 Chevrolet Corvette Z06')">
                                        <span>Buy Now</span>
                                        <small>or $1,850/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2024 Chevrolet Corvette Z06')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Chevrolet Performance</span>
                                        <span class="dealer-rating">★★★★★ (4.7)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 10 -->
                        <div class="car-card" data-brand="aston-martin" data-price="220000" data-year="2022">
                            <div class="car-badges">
                                <div class="car-badge featured">Featured</div>
                                <div class="car-badge inspected">Inspected</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=400&h=250&fit=crop" alt="Aston Martin" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2022 Aston Martin DB11</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(4.9)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$220,000</p>
                                    <p class="price-estimate">Est. $3,700/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>11,200 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Newport Beach, CA</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">V12 Twin-Turbo • 630 HP • 0-60 in 3.7s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Luxury Interior</span>
                                    <span class="feature-tag">Bang & Olufsen</span>
                                    <span class="feature-tag">Carbon Package</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2022 Aston Martin DB11')">
                                        <span>Buy Now</span>
                                        <small>or $3,700/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2022 Aston Martin DB11')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Aston Martin Newport</span>
                                        <span class="dealer-rating">★★★★★ (4.8)</span>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Car Card 11 -->
                        <div class="car-card" data-brand="lotus" data-price="2100000" data-year="2024">
                            <div class="car-badges">
                                <div class="car-badge new-arrival">New Arrival</div>
                                <div class="car-badge featured">Electric</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1592198084033-aade902d1aae?w=400&h=250&fit=crop" alt="Lotus Evija" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2024 Lotus Evija</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(5.0)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$2,100,000</p>
                                    <p class="price-estimate">Est. $35,000/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>500 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>London, UK</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⚡</span>
                                        <span>Electric</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Automatic</span>
                                    </div>
                                </div>
                                <p class="car-specs">Electric • 1972 HP • 0-60 in 2.3s • AWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Hypercar</span>
                                    <span class="feature-tag">Carbon Monocoque</span>
                                    <span class="feature-tag">Track Mode</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2024 Lotus Evija')">
                                        <span>Buy Now</span>
                                        <small>or $35,000/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2024 Lotus Evija')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Lotus Exclusive</span>
                                        <span class="dealer-rating">★★★★★ (5.0)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 12 -->
                        <div class="car-card" data-brand="koenigsegg" data-price="1900000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge featured">Featured</div>
                                <div class="car-badge certified">Hypercar</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=400&h=250&fit=crop" alt="Koenigsegg Regera" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 Koenigsegg Regera</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(5.0)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$1,900,000</p>
                                    <p class="price-estimate">Est. $32,000/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>1,200 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Stockholm, Sweden</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⚡</span>
                                        <span>Hybrid</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Direct Drive</span>
                                    </div>
                                </div>
                                <p class="car-specs">V8 Hybrid • 1500 HP • 0-60 in 2.8s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Regera Tech</span>
                                    <span class="feature-tag">Carbon Body</span>
                                    <span class="feature-tag">Ghost Package</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 Koenigsegg Regera')">
                                        <span>Buy Now</span>
                                        <small>or $32,000/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 Koenigsegg Regera')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Koenigsegg Official</span>
                                        <span class="dealer-rating">★★★★★ (5.0)</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Car Card 13 -->
                        <div class="car-card" data-brand="pagani" data-price="2800000" data-year="2023">
                            <div class="car-badges">
                                <div class="car-badge featured">Featured</div>
                                <div class="car-badge certified">Masterpiece</div>
                            </div>
                            <div class="car-image">
                                <img src="https://images.unsplash.com/photo-1617814076367-b759c7d7e738?w=400&h=250&fit=crop" alt="Pagani Huayra" loading="lazy">
                                <div class="image-overlay">
                                    <button class="overlay-btn favorite-btn" title="Add to Favorites">♡</button>
                                    <button class="overlay-btn compare-btn" title="Add to Compare">⚖</button>
                                    <button class="overlay-btn gallery-btn" title="View Gallery">📷</button>
                                </div>
                            </div>
                            <div class="car-info">
                                <div class="car-header">
                                    <h3>2023 Pagani Huayra</h3>
                                    <div class="car-rating">
                                        <span class="stars">★★★★★</span>
                                        <span class="rating-text">(5.0)</span>
                                    </div>
                                </div>
                                <div class="price-section">
                                    <p class="car-price">$2,800,000</p>
                                    <p class="price-estimate">Est. $47,000/mo</p>
                                </div>
                                <div class="car-details">
                                    <div class="detail-item">
                                        <span class="detail-icon">🛣️</span>
                                        <span>800 miles</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">📍</span>
                                        <span>Modena, Italy</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">⛽</span>
                                        <span>Premium Gas</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-icon">🔧</span>
                                        <span>Manual</span>
                                    </div>
                                </div>
                                <p class="car-specs">V12 Twin-Turbo • 730 HP • 0-60 in 3.2s • RWD</p>
                                <div class="car-features">
                                    <span class="feature-tag">Handcrafted</span>
                                    <span class="feature-tag">Carbon Art</span>
                                    <span class="feature-tag">Bespoke Interior</span>
                                </div>
                                <div class="car-actions">
                                    <button class="btn-primary" onclick="buyNow('2023 Pagani Huayra')">
                                        <span>Buy Now</span>
                                        <small>or $47,000/mo</small>
                                    </button>
                                    <button class="btn-secondary" onclick="viewDetails('2023 Pagani Huayra')">View Details</button>
                                </div>
                                <div class="dealer-info">
                                    <img src="https://via.placeholder.com/30x30" alt="Dealer" class="dealer-avatar">
                                    <div class="dealer-details">
                                        <span class="dealer-name">Pagani Automobili</span>
                                        <span class="dealer-rating">★★★★★ (5.0)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="slider-btn next-btn" onclick="moveSlider(1)">❯</button>
            </div>

            <div class="slider-dots" id="sliderDots">
                <!-- Dots will be generated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Luxury Showroom Section -->
    <section id="showroom" class="luxury-showroom">
        <div class="container">
            <h2 class="section-title showroom-title">🚗 Trusted Car Marketplace</h2>
            <p class="showroom-subtitle">Experience luxury at our premium automotive showroom</p>

            <!-- Showroom Building -->
            <div class="showroom-building">
                <!-- Logo on top of building -->
                <div class="building-logo">
                    <img src="logo.jpg" alt="AutoDeals Logo" class="showroom-logo">
                    <div class="logo-glow"></div>
                </div>

                <!-- Building Structure -->
                <div class="building-main">
                    <div class="building-roof"></div>
                    <div class="building-facade">
                        <!-- Glass Windows -->
                        <div class="window-row top-row">
                            <div class="window"></div>
                            <div class="window"></div>
                            <div class="window"></div>
                            <div class="window"></div>
                        </div>
                        <div class="window-row middle-row">
                            <div class="window large-window"></div>
                            <div class="window large-window"></div>
                        </div>

                        <!-- Showroom Floor with Cars -->
                        <div class="showroom-floor">
                            <div class="showroom-lights"></div>

                            <!-- Luxury Cars Display -->
                            <div class="cars-display">
                                <div class="display-car car-1">
                                    <div class="car-spotlight"></div>
                                    <div class="car-model">
                                        <img src="https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=300&h=150&fit=crop" alt="Luxury Car 1" class="car-image">
                                        <div class="car-label">Lamborghini Huracan</div>
                                        <div class="car-price">$250,000</div>
                                    </div>
                                </div>

                                <div class="display-car car-2">
                                    <div class="car-spotlight"></div>
                                    <div class="car-model">
                                        <img src="https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=300&h=150&fit=crop" alt="Luxury Car 2" class="car-image">
                                        <div class="car-label">Ferrari 488 GTB</div>
                                        <div class="car-price">$280,000</div>
                                    </div>
                                </div>

                                <div class="display-car car-3">
                                    <div class="car-spotlight"></div>
                                    <div class="car-model">
                                        <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=150&fit=crop" alt="Luxury Car 3" class="car-image">
                                        <div class="car-label">McLaren 720S</div>
                                        <div class="car-price">$300,000</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Showroom Features -->
                            <div class="showroom-features">
                                <div class="feature-item">
                                    <div class="feature-icon">🏆</div>
                                    <span>Premium Collection</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">✨</div>
                                    <span>Luxury Experience</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">🔒</div>
                                    <span>Secure Transactions</span>
                                </div>
                            </div>
                        </div>

                        <!-- Building Entrance -->
                        <div class="building-entrance">
                            <div class="entrance-doors">
                                <div class="door left-door"></div>
                                <div class="door right-door"></div>
                            </div>
                            <div class="entrance-steps"></div>
                        </div>
                    </div>
                </div>

                <!-- Building Base -->
                <div class="building-base"></div>

                <!-- Decorative Elements -->
                <div class="building-decorations">
                    <div class="decoration left-plant"></div>
                    <div class="decoration right-plant"></div>
                    <div class="decoration left-lamp"></div>
                    <div class="decoration right-lamp"></div>
                </div>
            </div>

            <!-- Showroom Stats -->
            <div class="showroom-stats">
                <div class="stat-card">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Luxury Vehicles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Premium Brands</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Concierge Service</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Satisfaction</div>
                </div>
            </div>

            <!-- Visit Showroom Button -->
            <div class="showroom-cta">
                <button class="visit-showroom-btn" onclick="visitShowroom()">
                    <span class="btn-text">Visit Our Showroom</span>
                    <span class="btn-icon">🏢</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Sell Your Car Section -->
    <section id="sell" class="sell-car">
        <div class="container">
            <h2 class="section-title">Sell Your Car</h2>
            <p class="section-subtitle">Get the best value for your vehicle with our free listing service</p>

            <div class="sell-content">
                <div class="sell-info">
                    <h3>Why Sell With AutoDeals?</h3>
                    <div class="sell-benefits">
                        <div class="benefit-item">
                            <div class="benefit-icon">✓</div>
                            <div class="benefit-text">
                                <h4>Free Listing</h4>
                                <p>List your car for free and reach thousands of buyers</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">✓</div>
                            <div class="benefit-text">
                                <h4>Professional Photos</h4>
                                <p>We'll take high-quality photos of your vehicle</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">✓</div>
                            <div class="benefit-text">
                                <h4>Secure Transactions</h4>
                                <p>Safe and secure payment processing</p>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">✓</div>
                            <div class="benefit-text">
                                <h4>Expert Support</h4>
                                <p>Our team helps you through the entire process</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sell-form-container">
                    <form class="sell-form">
                        <h3>Get Your Car's Value</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="make">Make</label>
                                <select id="make" name="make" required>
                                    <option value="">Select Make</option>
                                    <option value="bmw">BMW</option>
                                    <option value="mercedes">Mercedes-Benz</option>
                                    <option value="audi">Audi</option>
                                    <option value="porsche">Porsche</option>
                                    <option value="ferrari">Ferrari</option>
                                    <option value="lamborghini">Lamborghini</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="model">Model</label>
                                <input type="text" id="model" name="model" placeholder="e.g., 911, M3, C-Class" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="year">Year</label>
                                <select id="year" name="year" required>
                                    <option value="">Select Year</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                    <option value="2021">2021</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="mileage">Mileage</label>
                                <input type="number" id="mileage" name="mileage" placeholder="e.g., 25000" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="condition">Condition</label>
                            <select id="condition" name="condition" required>
                                <option value="">Select Condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="very-good">Very Good</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="contact-info">Contact Information</label>
                            <input type="email" id="contact-info" name="email" placeholder="Your email address" required>
                        </div>

                        <button type="submit" class="submit-button">Get Free Valuation</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <p class="section-subtitle">Simple steps to buy or sell your car</p>

            <div class="process-tabs">
                <button class="tab-button active" onclick="showTab('buying')">Buying a Car</button>
                <button class="tab-button" onclick="showTab('selling')">Selling a Car</button>
            </div>

            <div id="buying" class="tab-content active">
                <div class="process-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Search & Browse</h3>
                            <p>Use our advanced filters to find cars that match your preferences and budget</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Inspect & Test Drive</h3>
                            <p>Schedule a viewing and test drive. All our cars come with detailed inspection reports</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Secure Purchase</h3>
                            <p>Complete your purchase with our secure payment system and get instant ownership transfer</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="selling" class="tab-content">
                <div class="process-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Get Valuation</h3>
                            <p>Fill out our form to get an instant estimate of your car's market value</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Create Listing</h3>
                            <p>We'll help you create an attractive listing with professional photos and descriptions</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Complete Sale</h3>
                            <p>Connect with verified buyers and complete the sale with our secure transaction process</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Contact AutoDeals</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Need Help?</h3>
                    <p>Our customer support team is here to help you buy or sell your car with confidence.</p>
                    <div class="contact-details">
                        <div class="contact-item">
                            <span class="contact-icon">📧</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📱</span>
                            <span>+1 (800) AUTO-DEAL</span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📍</span>
                            <span>Nationwide Service</span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">🕒</span>
                            <span>Mon-Fri: 8AM-8PM, Sat-Sun: 9AM-6PM</span>
                        </div>
                    </div>

                    <div class="support-links">
                        <h4>Quick Links</h4>
                        <ul>
                            <li><a href="#faq">FAQ</a></li>
                            <li><a href="#financing">Financing Options</a></li>
                            <li><a href="#warranty">Vehicle Warranty</a></li>
                            <li><a href="#inspection">Inspection Reports</a></li>
                        </ul>
                    </div>
                </div>
                <form class="contact-form">
                    <h3>Send Us a Message</h3>
                    <div class="form-group">
                        <input type="text" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <select id="inquiry-type" name="inquiry-type" required>
                            <option value="">Select Inquiry Type</option>
                            <option value="buying">Buying a Car</option>
                            <option value="selling">Selling a Car</option>
                            <option value="financing">Financing Questions</option>
                            <option value="inspection">Vehicle Inspection</option>
                            <option value="support">General Support</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="How can we help you?" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-button">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>AutoDeals</h4>
                    <p>Your trusted online car marketplace for buying and selling quality vehicles.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#cars">Browse Cars</a></li>
                        <li><a href="#sell">Sell Your Car</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#financing">Auto Financing</a></li>
                        <li><a href="#inspection">Vehicle Inspection</a></li>
                        <li><a href="#warranty">Extended Warranty</a></li>
                        <li><a href="#insurance">Auto Insurance</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#faq">FAQ</a></li>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 AutoDeals. All rights reserved. | Licensed Car Dealer</p>
            </div>
        </div>
    </footer>

    <script>
        let currentSlide = 0;
        const totalSlides = 13;
        const slidesToShow = 3; // Number of cards to show at once
        const maxSlide = totalSlides - slidesToShow;

        function moveSlider(direction) {
            currentSlide += direction;

            if (currentSlide < 0) {
                currentSlide = 0;
            } else if (currentSlide > maxSlide) {
                currentSlide = maxSlide;
            }

            updateSlider();
        }

        function goToSlide(slideIndex) {
            currentSlide = slideIndex;
            if (currentSlide > maxSlide) {
                currentSlide = maxSlide;
            }
            updateSlider();
        }

        function updateSlider() {
            const sliderTrack = document.getElementById('sliderTrack');
            const cardWidth = 320; // Width of each card including margin
            const translateX = -currentSlide * cardWidth;

            sliderTrack.style.transform = `translateX(${translateX}px)`;

            // Update dots
            updateDots();
        }

        function updateDots() {
            const dots = document.querySelectorAll('.dot');
            dots.forEach((dot, index) => {
                dot.classList.remove('active');
                if (index === currentSlide) {
                    dot.classList.add('active');
                }
            });
        }

        function createDots() {
            const dotsContainer = document.getElementById('sliderDots');
            for (let i = 0; i <= maxSlide; i++) {
                const dot = document.createElement('span');
                dot.className = 'dot';
                if (i === 0) dot.classList.add('active');
                dot.onclick = () => goToSlide(i);
                dotsContainer.appendChild(dot);
            }
        }

        // Auto-slide functionality
        function autoSlide() {
            currentSlide++;
            if (currentSlide > maxSlide) {
                currentSlide = 0;
            }
            updateSlider();
        }

        // Tab functionality for How It Works section
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Search functionality
        function searchCars() {
            const searchInput = document.querySelector('.search-input').value;
            const brandSelect = document.querySelector('.search-select').value;

            // In a real application, this would filter the cars
            console.log('Searching for:', searchInput, 'Brand:', brandSelect);
            alert(`Searching for: ${searchInput} ${brandSelect ? 'Brand: ' + brandSelect : ''}`);
        }

        // Filter functionality
        function applyFilters() {
            const filters = document.querySelectorAll('.filter-select');
            const filterValues = Array.from(filters).map(filter => ({
                name: filter.previousElementSibling?.textContent || 'filter',
                value: filter.value
            }));

            console.log('Applying filters:', filterValues);
            alert('Filters applied! In a real app, this would filter the car listings.');
        }

        // Enhanced car action functions
        function buyNow(carName) {
            alert(`🚗 Initiating purchase process for ${carName}\n\n✓ Financing pre-approval\n✓ Vehicle inspection report\n✓ Secure payment processing\n\nIn a real app, this would redirect to checkout.`);
        }

        function viewDetails(carName) {
            alert(`📋 Viewing detailed information for ${carName}\n\n✓ Complete vehicle history\n✓ 360° interior/exterior photos\n✓ Maintenance records\n✓ Financing calculator\n\nIn a real app, this would open a detailed view.`);
        }

        // Favorite functionality
        function toggleFavorite(button) {
            const isFavorited = button.textContent === '♥';
            button.textContent = isFavorited ? '♡' : '♥';
            button.style.color = isFavorited ? '#333' : '#e74c3c';

            const carName = button.closest('.car-card').querySelector('h3').textContent;
            const action = isFavorited ? 'removed from' : 'added to';

            // Show a subtle notification
            showNotification(`${carName} ${action} favorites`);
        }

        // Compare functionality
        function toggleCompare(button) {
            const carCard = button.closest('.car-card');
            const carName = carCard.querySelector('h3').textContent;

            // Toggle compare state
            const isComparing = button.classList.toggle('comparing');
            button.style.backgroundColor = isComparing ? '#3498db' : 'rgba(255,255,255,0.9)';
            button.style.color = isComparing ? 'white' : '#333';

            const action = isComparing ? 'added to' : 'removed from';
            showNotification(`${carName} ${action} comparison list`);
        }

        // Gallery functionality
        function showGallery(button) {
            const carName = button.closest('.car-card').querySelector('h3').textContent;
            alert(`📸 Opening photo gallery for ${carName}\n\n✓ 20+ high-resolution photos\n✓ 360° exterior view\n✓ Interior details\n✓ Engine bay\n\nIn a real app, this would open an image gallery.`);
        }

        // Notification system
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: #2c3e50;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Animation and Scroll Effects
        function initAnimations() {
            // Loading animation
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = `
                <div>
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Loading AutoDeals...</div>
                </div>
            `;
            document.body.appendChild(loadingOverlay);

            // Remove loading overlay after page loads
            window.addEventListener('load', () => {
                setTimeout(() => {
                    loadingOverlay.classList.add('fade-out');
                    setTimeout(() => {
                        document.body.removeChild(loadingOverlay);
                    }, 500);
                }, 1000);
            });

            // Enhanced scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate', 'animated');

                        // Special handling for section titles
                        if (entry.target.classList.contains('section-title')) {
                            entry.target.style.animationDelay = '0s';
                        }

                        // Stagger animations for service cards
                        if (entry.target.classList.contains('service-card')) {
                            const delay = entry.target.getAttribute('data-delay') || '0s';
                            entry.target.style.transitionDelay = delay;
                        }

                        // Stagger animations for car cards
                        if (entry.target.classList.contains('car-card')) {
                            const cards = entry.target.parentElement.querySelectorAll('.car-card');
                            cards.forEach((card, index) => {
                                setTimeout(() => {
                                    card.classList.add('animate', 'animated');
                                }, index * 100);
                            });
                        }

                        // Add special effects for different sections
                        const section = entry.target.closest('section');
                        if (section) {
                            section.classList.add('section-visible');
                        }
                    }
                });
            }, observerOptions);

            // Section-specific animations
            const sectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionId = entry.target.id;

                        // Add specific animations based on section
                        switch(sectionId) {
                            case 'about':
                                entry.target.classList.add('about-animated');
                                break;
                            case 'services':
                                entry.target.classList.add('services-animated');
                                break;
                            case 'cars':
                                entry.target.classList.add('cars-animated');
                                break;
                            case 'sell':
                                entry.target.classList.add('sell-animated');
                                break;
                            case 'how-it-works':
                                entry.target.classList.add('works-animated');
                                break;
                            case 'contact':
                                entry.target.classList.add('contact-animated');
                                break;
                        }
                    }
                });
            }, { threshold: 0.2 });

            // Observe elements for scroll animations
            document.querySelectorAll('.section-title, .animate-on-scroll, .car-card, .service-card, .step').forEach(el => {
                observer.observe(el);
            });

            // Observe sections for background animations
            document.querySelectorAll('section[id]').forEach(section => {
                sectionObserver.observe(section);
            });

            // Header scroll effect
            let lastScrollTop = 0;
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const header = document.querySelector('.header');

                if (scrollTop > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }

                // Parallax effect for hero section
                const hero = document.querySelector('.hero');
                if (hero) {
                    const scrolled = window.pageYOffset;
                    const parallax = scrolled * 0.5;
                    hero.style.transform = `translateY(${parallax}px)`;
                }

                lastScrollTop = scrollTop;
            });

            // Smooth scroll for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add hover animations to cards
            document.querySelectorAll('.car-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Animate buttons on hover
            document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // Showroom functionality
        function visitShowroom() {
            alert(`🏢 Welcome to AutoDeals Luxury Showroom!\n\n✨ Premium Experience Awaits:\n• Private viewing appointments\n• Expert consultations\n• Test drive arrangements\n• Financing assistance\n• Concierge service\n\n📍 Visit us at our flagship location\n📞 Call: +1 (800) AUTO-DEAL\n\nIn a real app, this would redirect to showroom booking.`);
        }

        // Showroom car interactions
        function showroomCarDetails(carName) {
            alert(`🚗 ${carName} - Showroom Details\n\n✓ Available for immediate viewing\n✓ Test drive ready\n✓ Full documentation available\n✓ Financing options\n✓ Trade-in evaluation\n\nSchedule your private viewing today!`);
        }

        // Initialize slider and other functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize animations
            initAnimations();

            createDots();

            // Auto-slide every 4 seconds
            setInterval(autoSlide, 4000);

            // Pause auto-slide on hover
            const sliderContainer = document.querySelector('.slider-container');
            let autoSlideInterval;

            sliderContainer.addEventListener('mouseenter', () => {
                clearInterval(autoSlideInterval);
            });

            sliderContainer.addEventListener('mouseleave', () => {
                autoSlideInterval = setInterval(autoSlide, 4000);
            });

            // Add event listeners for search and filter buttons
            const searchButton = document.querySelector('.search-button');
            if (searchButton) {
                searchButton.addEventListener('click', searchCars);
            }

            const filterButton = document.querySelector('.filter-button');
            if (filterButton) {
                filterButton.addEventListener('click', applyFilters);
            }

            // Add event listeners for car action buttons
            document.querySelectorAll('.btn-primary').forEach(button => {
                button.addEventListener('click', function() {
                    const carName = this.closest('.car-card').querySelector('h3').textContent;
                    buyNow(carName);
                });
            });

            document.querySelectorAll('.btn-secondary').forEach(button => {
                button.addEventListener('click', function() {
                    const carName = this.closest('.car-card').querySelector('h3').textContent;
                    viewDetails(carName);
                });
            });

            // Add event listeners for overlay buttons
            document.querySelectorAll('.favorite-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleFavorite(this);
                });
            });

            document.querySelectorAll('.compare-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleCompare(this);
                });
            });

            document.querySelectorAll('.gallery-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    showGallery(this);
                });
            });

            // Form submissions
            const sellForm = document.querySelector('.sell-form');
            if (sellForm) {
                sellForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Thank you! We\'ll send you a valuation within 24 hours.');
                });
            }

            const contactForm = document.querySelector('.contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Thank you for your message! We\'ll get back to you soon.');
                });
            }
        });
    </script>
</body>
</html>
